import { observer } from 'mobx-react-lite';
import * as React from 'react';

import { Tabs } from '../Tabs';
import store from '../../store';
import { eventUtils } from '@browser/core/utils/platform-lite';
import { TabGroup } from '../TabGroup';
import { ICON_ADD, TOOLBAR_BUTTON_WIDTH, ADD_TAB_BUTTON_WIDTH, ADD_TAB_BUTTON_HEIGHT } from '@mario-ai/shared';
import { AddressBarContainer } from '../AddressBarContainer';
import { ToolbarButton } from '../ToolbarButton';
import { cn } from '@browser/utils/tailwind-helpers';

let timeout: any;

const onMouseEnter = () => {
  clearTimeout(timeout);
};

const onTabsMouseLeave = () => {
  timeout = setTimeout(() => {
    store.tabs.removedTabs = 0;
    store.tabs.updateTabsBounds(true);
  }, 300);
};

const onAddTabClick = () => {
  store.tabs.addTab();
};

const onWheel = (e: any) => {
  if (!store.tabs.containerRef) return;

  const { deltaX, deltaY } = e;
  const { scrollLeft } = store.tabs.containerRef.current;

  const delta = Math.abs(deltaX) >= Math.abs(deltaY) ? deltaX : -deltaY;
  const target = delta / 2;

  store.tabs.scrollingToEnd = false;

  store.tabs.containerRef.current.scrollLeft = scrollLeft + target;
};

export const TabGroups = observer(() => {
  return (
    <React.Fragment>
      {store.tabGroups.list.map((item) => (
        <TabGroup key={item.id} tabGroup={item}></TabGroup>
      ))}
    </React.Fragment>
  );
});

export const Tabbar = observer(() => {
  // StyledTabbar 样式 - 对应原始 StyledTabbar
  const tabbarClasses = cn(
    // 原始：height: 100%; width: 100%; position: relative; overflow: hidden; align-items: center; margin-right: 28px; display: flex; margin-left: 0px;
    'h-full w-full relative overflow-hidden items-center mr-7 flex ml-0'
  );

  // TabsContainer 样式 - 修复容器宽度问题
  const tabsContainerClasses = cn(
    'h-full relative overflow-hidden whitespace-nowrap',
    // 确保容器有实际宽度
    'flex-1',
    // 原始样式：overflow-x: overlay
    'overflow-x-auto',
    // 隐藏滚动条
    '[&::-webkit-scrollbar]:h-0 [&::-webkit-scrollbar]:hidden [&::-webkit-scrollbar]:bg-transparent [&::-webkit-scrollbar]:opacity-0'
  );

  const tabsContainerStyle = {
    // 确保容器有最小宽度
    minWidth: '200px',
    width: `calc(100% - ${TOOLBAR_BUTTON_WIDTH}px)`,
  };

  // AddTab 样式 - 对应原始 AddTab styled component
  const addTabClasses = cn(
    'absolute rounded-full'
  );

  const addTabStyle = {
    position: 'absolute',
    left: '0px', // 原始样式：left: 0
    minWidth: `${ADD_TAB_BUTTON_WIDTH}px`,
    height: `${ADD_TAB_BUTTON_HEIGHT}px`,
    top: store.isCompact ? 'auto' : `${(store.theme.tabMarginTop || 4) + 2}px`,
    borderRadius: '99999px',
  };





  return (
    <div className={tabbarClasses}>
      <div
        className={tabsContainerClasses}
        style={tabsContainerStyle}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onTabsMouseLeave}
        onWheel={onWheel}
        ref={store.tabs.containerRef}
      >
        <TabGroups />
        <Tabs />
      </div>
      <ToolbarButton
        className={addTabClasses}
        style={{
          ...addTabStyle,
          transform: `translateX(${store.addTab.left}px)` // 使用store中的动态位置
        }}
        icon={ICON_ADD}
        onClick={onAddTabClick}
        divRef={(r: any) => (store.addTab.ref = r)}
      />
      {store.isCompact && <AddressBarContainer />}
    </div>
  );
});
