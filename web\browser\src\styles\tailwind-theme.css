@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* AI工具栏主题变量 - Light主题 */
    --mario-ai-toolbar-bg: #ffffff;
    --mario-border-color: #e0e0e0;
    --mario-hover-bg: rgba(0, 0, 0, 0.05);
    --mario-active-bg: rgba(0, 0, 0, 0.1);
    --mario-text-secondary: #666666;
    --mario-tooltip-bg: #333333;
    --mario-tooltip-text: #ffffff;

    /* 浅色主题的页面背景变量 */
    --mario-page-bg: #f5f5f5;
    --mario-page-text: #333333;
    /* Light Theme - 与 lightTheme 完全一致 */
    --mario-titlebar-bg: #f3f3fa;
    --mario-addressbar-bg: #fff;
    --mario-addressbar-text: #000;
    --mario-toolbar-bg: #f3f3fa;
    --mario-toolbar-bottom-line: rgba(0, 0, 0, 0.12);
    --mario-toolbar-separator: rgba(0, 0, 0, 0.12);
    --mario-tab-text: rgba(0, 0, 0, 0.7);
    --mario-tab-selected-text: #000;
    --mario-control-bg: rgba(0, 0, 0, 0.08);
    --mario-control-hover-bg: rgba(0, 0, 0, 0.1);
    --mario-control-value: #000;
    --mario-switch-bg: rgba(0, 0, 0, 0.16);
    --mario-dialog-bg: #fff;
    --mario-dialog-text: #000;
    --mario-dialog-separator: rgba(0, 0, 0, 0.12);
    --mario-searchbox-bg: #fff;
    --mario-page-bg: #fff;
    --mario-page-text: #000;
    --mario-nav-drawer1-bg: #f5f5f5;
    --mario-nav-drawer2-bg: #fafafa;
    --mario-dropdown-bg: #fff;
    --mario-dropdown-bg-translucent: rgba(255, 255, 255, 0.7);
    --mario-dropdown-separator: rgba(0, 0, 0, 0.12);
    --mario-accent: #2196F3;

    /* 图标过滤器 */
    --icon-filter: none;
  }

  [data-theme="dark"] {
    /* AI工具栏主题变量 - Dark主题 */
    --mario-ai-toolbar-bg: #2d2d2d;
    --mario-border-color: #555555;
    --mario-hover-bg: rgba(255, 255, 255, 0.1);
    --mario-active-bg: rgba(255, 255, 255, 0.15);
    --mario-text-secondary: #cccccc;
    --mario-tooltip-bg: #1a1a1a;
    --mario-tooltip-text: #ffffff;

    /* Dark Theme - 与 darkTheme 完全一致 */
    --mario-titlebar-bg: #1c1c1c;
    --mario-addressbar-bg: #393939;
    --mario-addressbar-text: #fff;
    --mario-toolbar-bg: #1c1c1c;
    --mario-toolbar-bottom-line: rgba(255, 255, 255, 0.08);
    --mario-toolbar-separator: rgba(255, 255, 255, 0.12);
    --mario-tab-text: rgba(255, 255, 255, 0.54);
    --mario-tab-selected-text: #fff;
    --mario-control-bg: rgba(255, 255, 255, 0.1);
    --mario-control-hover-bg: rgba(255, 255, 255, 0.12);
    --mario-control-value: #fff;
    --mario-switch-bg: rgba(255, 255, 255, 0.24);
    --mario-dialog-bg: #383838;
    --mario-dialog-text: #fff;
    --mario-dialog-separator: rgba(255, 255, 255, 0.12);
    --mario-searchbox-bg: #262626;
    --mario-page-bg: #212121;
    --mario-page-text: #fff;
    --mario-nav-drawer1-bg: rgba(255, 255, 255, 0.1);
    --mario-nav-drawer2-bg: rgba(255, 255, 255, 0.05);
    --mario-dropdown-bg: rgb(66, 66, 66);
    --mario-dropdown-bg-translucent: rgba(60, 60, 60, 0.6);
    --mario-dropdown-separator: rgba(255, 255, 255, 0.12);
    --mario-accent: #2196F3;

    /* 图标过滤器 - 深色主题下反转图标颜色 */
    --icon-filter: invert(100%);

    /* 深色主题的前卫主题变量兼容 */
    --titlebar-bg: var(--mario-titlebar-bg);
    --toolbar-bg: var(--mario-toolbar-bg);
    --bookmark-bar-bg: var(--mario-toolbar-bg);

    /* 深色主题的页面背景变量 */
    --mario-page-bg: #212121;
    --mario-page-text: #ffffff;
  }

  [data-theme="futuristic"] {
    /* AI工具栏主题变量 - Opera风格深色主题 */
    --mario-ai-toolbar-bg: #1a1a1a;
    --mario-border-color: #404040;
    --mario-hover-bg: rgba(255, 255, 255, 0.1);
    --mario-active-bg: rgba(255, 255, 255, 0.15);
    --mario-text-secondary: #e0e0e0;
    --mario-tooltip-bg: #333333;
    --mario-tooltip-text: #ffffff;

    /* 浏览器头部栏 - Opera风格深灰色 */
    --titlebar-bg: #1a1a1a;
    --toolbar-bg: #2d2d2d;
    --bookmark-bar-bg: #2d2d2d;

    /* 首页背景 - Opera风格深色 */
    --home-bg: #1a1a1a;

    /* 文字颜色 - Opera风格 */
    --titlebar-text: #ffffff;
    --toolbar-text: #ffffff;

    /* 按钮和输入框 - Opera风格 */
    --button-bg: #333333;
    --button-hover-bg: #3a3a3a;
    --input-bg: #333333;
    --input-border: #404040;

    /* Opera风格UI元素 */
    --mario-titlebar-bg: #1a1a1a;
    --mario-addressbar-bg: #333333;
    --mario-addressbar-text: #ffffff;
    --mario-toolbar-bg: #2d2d2d;
    --mario-toolbar-bottom-line: #404040;
    --mario-toolbar-separator: #404040;
    --mario-tab-text: #e0e0e0;
    --mario-tab-selected-text: #ffffff;
    --mario-control-bg: #333333;
    --mario-control-hover-bg: #3a3a3a;
    --mario-control-value: #ffffff;
    --mario-switch-bg: #404040;
    --mario-dialog-bg: #2d2d2d;
    --mario-dialog-text: #ffffff;
    --mario-dialog-separator: #404040;
    --mario-searchbox-bg: #333333;
    --mario-page-bg: #1a1a1a;
    --mario-page-text: #ffffff;
    --mario-nav-drawer1-bg: #333333;
    --mario-nav-drawer2-bg: #2d2d2d;
    --mario-dropdown-bg: #2d2d2d;
    --mario-dropdown-bg-translucent: rgba(45, 45, 45, 0.9);
    --mario-dropdown-separator: #404040;
    --mario-accent: #ff1b2d;

    /* 图标过滤器 - 前卫主题使用白色图标 */
    --icon-filter: brightness(0) invert(1);
  }

  /* 基础样式重置 - 与 baseStyle 一致 */
  body {
    user-select: none;
    cursor: default;
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    font-family: system-ui, sans-serif;
  }

  * {
    box-sizing: border-box;
  }

  /* RadioButton 伪元素样式支持 */
  .radio-button-circle::before {
    border-color: var(--before-border-color);
    transition: var(--before-transition);
  }

  /* NavigationDrawer 搜索图标支持 */
  .search-container::after {
    background-image: var(--after-bg-image);
    background-size: var(--after-bg-size);
  }

  /* 图标过滤器类 */
  .filter-mario-icon {
    filter: var(--icon-filter);
  }
}
