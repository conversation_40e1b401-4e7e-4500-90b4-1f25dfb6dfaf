<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Opera风格主题预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1a365d 100%);
            color: rgba(255, 255, 255, 0.95);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }

        .toolbar {
            background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
            padding: 10px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .bookmark-bar {
            background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
            padding: 8px 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .button {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.95);
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
            backdrop-filter: blur(10px);
        }

        .button:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .button.accent {
            background: #ff6b35;
        }

        .button.accent:hover {
            background: #e55a2b;
        }

        .input {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 15px;
            border-radius: 6px;
            width: 300px;
            backdrop-filter: blur(10px);
        }

        .input:focus {
            outline: none;
            border-color: #ff6b35;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .text-secondary {
            color: rgba(255, 255, 255, 0.8);
        }

        .separator {
            height: 1px;
            background: rgba(255, 255, 255, 0.2);
            margin: 20px 0;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .color-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }

        .color-sample {
            width: 100%;
            height: 60px;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        .bg-primary { background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); }
        .bg-secondary { background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%); }
        .bg-tertiary { background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%); }
        .bg-border { background: rgba(255, 255, 255, 0.2); }
        .bg-hover { background: rgba(255, 255, 255, 0.1); }
        .bg-accent { background: #ff6b35; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>🚀 Opera风格深蓝渐变主题预览</h1>
            <p class="text-secondary">Mario AI浏览器 - 前卫主题 (Opera深蓝渐变风格)</p>
        </div>
    </div>

    <div class="container">
        <div class="toolbar">
            <input type="text" class="input" placeholder="搜索或输入网址..." value="https://example.com">
            <button class="button">后退</button>
            <button class="button">前进</button>
            <button class="button">刷新</button>
            <button class="button accent">新标签页</button>
        </div>

        <div class="bookmark-bar">
            <span class="text-secondary">书签栏：</span>
            <button class="button" style="margin-left: 10px;">Google</button>
            <button class="button">GitHub</button>
            <button class="button">Stack Overflow</button>
        </div>

        <div class="card">
            <h2>🎨 主题特色</h2>
            <div class="separator"></div>
            <ul>
                <li><strong>Opera深蓝渐变</strong>：类似Opera浏览器的深蓝色渐变背景</li>
                <li><strong>现代化设计</strong>：半透明元素配合毛玻璃效果</li>
                <li><strong>橙色强调色</strong>：用于重要操作和链接的暖色调</li>
                <li><strong>优秀对比度</strong>：白色文本在深蓝背景上清晰可读</li>
                <li><strong>专业美观</strong>：既有科技感又不失优雅</li>
            </ul>
        </div>

        <div class="card">
            <h2>🔧 交互演示</h2>
            <div class="separator"></div>
            <p class="text-secondary">悬停下面的按钮查看效果：</p>
            <br>
            <button class="button">普通按钮</button>
            <button class="button accent">强调按钮</button>
            <input type="text" class="input" placeholder="输入框示例">
        </div>

        <div class="color-palette">
            <div class="color-item">
                <div class="color-sample bg-primary"></div>
                <strong>标题栏渐变</strong><br>
                <code>#1e3c72 → #2a5298</code><br>
                <span class="text-secondary">标题栏、主背景</span>
            </div>
            <div class="color-item">
                <div class="color-sample bg-secondary"></div>
                <strong>工具栏渐变</strong><br>
                <code>#2a5298 → #1e3c72</code><br>
                <span class="text-secondary">工具栏、导航栏</span>
            </div>
            <div class="color-item">
                <div class="color-sample bg-tertiary"></div>
                <strong>书签栏渐变</strong><br>
                <code>#1a365d → #2c5282</code><br>
                <span class="text-secondary">书签栏、侧边栏</span>
            </div>
            <div class="color-item">
                <div class="color-sample bg-border"></div>
                <strong>边框色</strong><br>
                <code>rgba(255,255,255,0.2)</code><br>
                <span class="text-secondary">分隔线、边框</span>
            </div>
            <div class="color-item">
                <div class="color-sample bg-hover"></div>
                <strong>半透明控件</strong><br>
                <code>rgba(255,255,255,0.1)</code><br>
                <span class="text-secondary">按钮、输入框</span>
            </div>
            <div class="color-item">
                <div class="color-sample bg-accent"></div>
                <strong>橙色强调</strong><br>
                <code>#ff6b35</code><br>
                <span class="text-secondary">强调色、链接</span>
            </div>
        </div>

        <div class="card" style="margin-top: 30px;">
            <h2>📝 使用说明</h2>
            <div class="separator"></div>
            <ol>
                <li>在浏览器设置中选择"🚀 前卫主题"</li>
                <li>或使用AI工具栏的主题切换按钮</li>
                <li>主题会立即应用到所有页面和组件</li>
                <li>享受专业、现代的Opera风格体验</li>
            </ol>
        </div>
    </div>
</body>
</html>
