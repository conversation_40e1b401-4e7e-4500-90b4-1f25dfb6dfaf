<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Opera风格主题预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #1a1a1a;
            padding: 15px 0;
            border-bottom: 1px solid #404040;
            margin-bottom: 30px;
        }

        .toolbar {
            background: #2d2d2d;
            padding: 10px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .bookmark-bar {
            background: #2d2d2d;
            padding: 8px 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .button {
            background: #333333;
            color: #ffffff;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .button:hover {
            background: #3a3a3a;
        }

        .button.accent {
            background: #ff1b2d;
        }

        .button.accent:hover {
            background: #e6182a;
        }

        .input {
            background: #333333;
            color: #ffffff;
            border: 1px solid #404040;
            padding: 10px 15px;
            border-radius: 6px;
            width: 300px;
        }

        .input:focus {
            outline: none;
            border-color: #ff1b2d;
        }

        .card {
            background: #2d2d2d;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .text-secondary {
            color: #e0e0e0;
        }

        .separator {
            height: 1px;
            background: #404040;
            margin: 20px 0;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .color-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #404040;
        }

        .color-sample {
            width: 100%;
            height: 60px;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        .bg-primary { background: #1a1a1a; }
        .bg-secondary { background: #2d2d2d; }
        .bg-tertiary { background: #333333; }
        .bg-border { background: #404040; }
        .bg-hover { background: #3a3a3a; }
        .bg-accent { background: #ff1b2d; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>🚀 Opera风格主题预览</h1>
            <p class="text-secondary">Mario AI浏览器 - 前卫主题 (Opera风格)</p>
        </div>
    </div>

    <div class="container">
        <div class="toolbar">
            <input type="text" class="input" placeholder="搜索或输入网址..." value="https://example.com">
            <button class="button">后退</button>
            <button class="button">前进</button>
            <button class="button">刷新</button>
            <button class="button accent">新标签页</button>
        </div>

        <div class="bookmark-bar">
            <span class="text-secondary">书签栏：</span>
            <button class="button" style="margin-left: 10px;">Google</button>
            <button class="button">GitHub</button>
            <button class="button">Stack Overflow</button>
        </div>

        <div class="card">
            <h2>🎨 主题特色</h2>
            <div class="separator"></div>
            <ul>
                <li><strong>专业深色背景</strong>：类似Opera浏览器的深灰色调</li>
                <li><strong>现代化设计</strong>：简洁、实用、不花哨</li>
                <li><strong>Opera红色强调</strong>：用于重要操作和链接</li>
                <li><strong>优秀对比度</strong>：确保文本清晰可读</li>
                <li><strong>护眼效果</strong>：适合长时间使用</li>
            </ul>
        </div>

        <div class="card">
            <h2>🔧 交互演示</h2>
            <div class="separator"></div>
            <p class="text-secondary">悬停下面的按钮查看效果：</p>
            <br>
            <button class="button">普通按钮</button>
            <button class="button accent">强调按钮</button>
            <input type="text" class="input" placeholder="输入框示例">
        </div>

        <div class="color-palette">
            <div class="color-item">
                <div class="color-sample bg-primary"></div>
                <strong>主背景色</strong><br>
                <code>#1a1a1a</code><br>
                <span class="text-secondary">标题栏、主背景</span>
            </div>
            <div class="color-item">
                <div class="color-sample bg-secondary"></div>
                <strong>次要背景色</strong><br>
                <code>#2d2d2d</code><br>
                <span class="text-secondary">工具栏、对话框</span>
            </div>
            <div class="color-item">
                <div class="color-sample bg-tertiary"></div>
                <strong>控件背景色</strong><br>
                <code>#333333</code><br>
                <span class="text-secondary">按钮、输入框</span>
            </div>
            <div class="color-item">
                <div class="color-sample bg-border"></div>
                <strong>边框色</strong><br>
                <code>#404040</code><br>
                <span class="text-secondary">分隔线、边框</span>
            </div>
            <div class="color-item">
                <div class="color-sample bg-hover"></div>
                <strong>悬停色</strong><br>
                <code>#3a3a3a</code><br>
                <span class="text-secondary">按钮悬停效果</span>
            </div>
            <div class="color-item">
                <div class="color-sample bg-accent"></div>
                <strong>Opera红</strong><br>
                <code>#ff1b2d</code><br>
                <span class="text-secondary">强调色、链接</span>
            </div>
        </div>

        <div class="card" style="margin-top: 30px;">
            <h2>📝 使用说明</h2>
            <div class="separator"></div>
            <ol>
                <li>在浏览器设置中选择"🚀 前卫主题"</li>
                <li>或使用AI工具栏的主题切换按钮</li>
                <li>主题会立即应用到所有页面和组件</li>
                <li>享受专业、现代的Opera风格体验</li>
            </ol>
        </div>
    </div>
</body>
</html>
