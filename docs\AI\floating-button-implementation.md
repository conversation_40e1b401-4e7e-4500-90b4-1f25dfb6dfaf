# AI工具栏极简悬浮按钮方案（方案J）

## 概述

在主窗口左侧边缘放置一个4px宽的悬浮条，悬停时展开为完整工具栏。

## 方案特点

- **极简设计**：默认只显示4px宽的指示条
- **悬停展开**：鼠标悬停时展开为64px宽的完整工具栏
- **零冲突**：不与BrowserView产生层级冲突
- **轻量实现**：使用CSS动画，无需独立进程

## 核心实现

### 1. 悬浮按钮组件

**文件**: `web/browser/src/views/app/components/FloatingAIButton/index.tsx`

```typescript
import { observer } from 'mobx-react-lite';
import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
import store from '../../store';

export const FloatingAIButton = observer(() => {
  const [isExpanded, setIsExpanded] = React.useState(false);
  const [isHovered, setIsHovered] = React.useState(false);

  // 处理工具点击
  const handleToolClick = (toolId: string) => {
    const existingView = store.tabs.list.find(tab => 
      tab.url.includes(`ai-${toolId}`)
    );
    
    if (existingView) {
      store.tabs.selectTab(existingView.id);
    } else {
      store.tabs.addTab({ 
        url: `mario-ai://ai-${toolId}`, 
        active: true 
      });
    }
    
    // 点击后收起
    setIsExpanded(false);
  };

  // 悬浮条样式
  const floatingBarClasses = cn(
    'fixed left-0 top-0 h-full z-[9999] transition-all duration-300 ease-out',
    'bg-gradient-to-b from-blue-500 to-purple-600',
    'hover:shadow-lg cursor-pointer',
    // 默认4px宽，悬停时展开
    isHovered || isExpanded ? 'w-16' : 'w-1',
    // 主题适配
    'dark:from-blue-600 dark:to-purple-700'
  );

  // 工具栏内容样式
  const toolbarContentClasses = cn(
    'h-full flex flex-col items-center py-4 transition-opacity duration-200',
    isHovered || isExpanded ? 'opacity-100' : 'opacity-0'
  );

  // 工具按钮样式
  const toolButtonClasses = cn(
    'w-10 h-10 rounded-lg mb-2 flex items-center justify-center',
    'text-white hover:bg-white/20 transition-colors duration-200',
    'text-lg cursor-pointer'
  );

  return (
    <div
      className={floatingBarClasses}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => setIsExpanded(!isExpanded)}
    >
      <div className={toolbarContentClasses}>
        {/* AI Logo */}
        <div className="text-2xl mb-4 opacity-80">🤖</div>
        
        {/* 工具按钮 */}
        <div 
          className={toolButtonClasses}
          onClick={(e) => {
            e.stopPropagation();
            handleToolClick('notes');
          }}
          title="智能笔记 (Alt+1)"
        >
          📝
        </div>
        
        <div 
          className={toolButtonClasses}
          onClick={(e) => {
            e.stopPropagation();
            handleToolClick('memory');
          }}
          title="AI记忆 (Alt+2)"
        >
          🧠
        </div>
        
        <div 
          className={toolButtonClasses}
          onClick={(e) => {
            e.stopPropagation();
            handleToolClick('clipboard');
          }}
          title="智能剪贴板 (Alt+3)"
        >
          📋
        </div>
        
        {/* 分隔线 */}
        <div className="w-8 h-px bg-white/30 my-2"></div>
        
        {/* 设置按钮 */}
        <div 
          className={toolButtonClasses}
          onClick={(e) => {
            e.stopPropagation();
            handleToolClick('settings');
          }}
          title="设置 (Alt+0)"
        >
          ⚙️
        </div>
      </div>
    </div>
  );
});
```

### 2. 集成到主应用

**文件**: `web/browser/src/views/app/components/App/index.tsx`

```typescript
import { FloatingAIButton } from '../FloatingAIButton';

const App = observer(() => {
  // 不需要修改marginLeft，悬浮按钮不占用布局空间
  const appStyle: React.CSSProperties = {
    height: 'auto',
    // 移除marginLeft，悬浮按钮使用fixed定位
  };

  return (
    <>
      {/* 悬浮AI按钮 */}
      <FloatingAIButton />
      
      {/* 主应用内容 */}
      <div
        className={appClasses}
        style={appStyle}
        onMouseOver={store.isFullscreen ? onAppEnter : undefined}
        onMouseLeave={store.isFullscreen ? onAppLeave : undefined}
      >
        <UIStyle />
        <Titlebar />
        {store.settings.object.topBarVariant === 'default' && <Toolbar />}
        <BookmarkBar />
      </div>

      <div
        className={lineClasses}
        style={lineStyle}
        onMouseOver={onLineEnter}
      />
    </>
  );
});
```

### 3. 全局快捷键支持

**文件**: `electron/src/main/services/ai-shortcuts.ts`

```typescript
import { globalShortcut } from 'electron';
import { AppWindow } from '../ui/windows';

export class AIShortcuts {
  private parentWindow: AppWindow;

  constructor(parentWindow: AppWindow) {
    this.parentWindow = parentWindow;
    this.registerShortcuts();
  }

  private registerShortcuts() {
    const shortcuts = [
      { key: 'Alt+1', tool: 'notes' },
      { key: 'Alt+2', tool: 'memory' },
      { key: 'Alt+3', tool: 'clipboard' },
      { key: 'Alt+0', tool: 'settings' },
    ];

    shortcuts.forEach(({ key, tool }) => {
      globalShortcut.register(key, () => {
        this.parentWindow.webContents.send('open-ai-tool', tool);
      });
    });
  }

  public destroy() {
    globalShortcut.unregisterAll();
  }
}
```

### 4. 主题适配

**文件**: `web/browser/tailwind.config.js`

```javascript
module.exports = {
  theme: {
    extend: {
      zIndex: {
        '9999': '9999', // 确保悬浮按钮在最顶层
      },
      animation: {
        'slide-in': 'slideIn 0.3s ease-out',
        'slide-out': 'slideOut 0.3s ease-in',
      },
      keyframes: {
        slideIn: {
          '0%': { width: '4px', opacity: '0.7' },
          '100%': { width: '64px', opacity: '1' },
        },
        slideOut: {
          '0%': { width: '64px', opacity: '1' },
          '100%': { width: '4px', opacity: '0.7' },
        },
      }
    }
  }
}
```

## 用户体验

### 默认状态
- 左侧边缘显示4px宽的渐变色条
- 不干扰正常浏览
- 视觉提示AI功能存在

### 悬停状态
- 平滑展开为64px宽的工具栏
- 显示所有AI工具按钮
- 保持在最顶层，不被遮挡

### 点击交互
- 直接点击工具按钮打开对应功能
- 支持全局快捷键
- 点击后自动收起

## 优势

1. **零布局冲突**：使用fixed定位，不影响BrowserView
2. **极简美观**：默认几乎不可见，需要时才展开
3. **性能优异**：纯CSS动画，无额外进程
4. **用户友好**：渐进式展示，不突兀
5. **易于维护**：纯React组件，逻辑简单

## 实施步骤

1. **创建FloatingAIButton组件** (2小时)
2. **集成到主应用** (30分钟)
3. **添加快捷键支持** (1小时)
4. **主题和动画优化** (1小时)
5. **测试和调优** (1小时)

## 预期效果

- ✅ 内存占用：0MB额外开销
- ✅ 视觉冲突：完全避免
- ✅ 用户体验：渐进式，不干扰
- ✅ 开发维护：简单直观
- ✅ 扩展性：易于添加新功能
