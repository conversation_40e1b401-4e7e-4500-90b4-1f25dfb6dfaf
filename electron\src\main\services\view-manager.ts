import {ipcMain} from 'electron';
import {VIEW_Y_OFFSET} from '@electron/renderer/constants/design';
import {View} from '@electron/main/services/view';
import {AppWindow} from '@electron/main/ui/windows';
import {ELECTRON_WEBUI_BASE_URL} from '@electron/renderer/constants/files';

import {
  ZOOM_FACTOR_MIN,
  ZOOM_FACTOR_MAX,
  ZOOM_FACTOR_INCREMENT,
} from '@electron/renderer/constants/web-contents';
import {EventEmitter} from 'events';
import {Application} from '@electron/main/core/application';

export class ViewManager extends EventEmitter {
  public views = new Map<number, View>();
  public selectedId = 0;
  public _fullscreen = false;

  public incognito: boolean;

  private window: AppWindow;

  public get fullscreen() {
    return this._fullscreen;
  }

  public getById(id: number) {
    return this.views.get(id);
  }

  public set fullscreen(val: boolean) {
    this._fullscreen = val;
    this.fixBounds();
  }

  public constructor(window: AppWindow, incognito: boolean) {
    super();

    this.window = window;
    this.incognito = incognito;

    const {id} = window.win;
    ipcMain.handle(`view-create-${id}`, (e, details) => {
      return this.create(details, false, false).id;
    });

    ipcMain.handle(`views-create-${id}`, (e, options) => {
      return options.map((option: any) => {
        return this.create(option, false, false).id;
      });
    });

    ipcMain.on(`add-tab-${id}`, (e, details) => {
      this.create(details);
    });

    ipcMain.on('open-dev-tool', (e, details) => {
      //this.views.get(this.selectedId).webContents.print();
      this.selected.webContents.inspectElement(0, 0);
      if (this.selected.webContents.isDevToolsOpened()) {
        this.selected.webContents.devToolsWebContents.focus();
      }
    });

    ipcMain.handle(`view-select-${id}`, (e, id: number, focus: boolean) => {
      return this.select(id, focus, true);
    });

    ipcMain.on(`view-destroy-${id}`, (e, id: number) => {
      this.destroy(id);
    });

    ipcMain.on(`mute-view-${id}`, (e, tabId: number) => {
      const view = this.views.get(tabId);
      view.webContents.setAudioMuted(true);
    });

    ipcMain.on(`unmute-view-${id}`, (e, tabId: number) => {
      const view = this.views.get(tabId);
      view.webContents.setAudioMuted(false);
    });

    ipcMain.on(`browserview-clear-${id}`, () => {
      this.clear();
    });

    ipcMain.on(`browserview-show-${id}`, (e) => {
      // 原始工程中的browserview-show处理器
      // 用于预处理或状态同步，实际的view切换由view-select处理
    });

    ipcMain.on('change-zoom', (e, zoomDirection) => {
      this.changeZoom(zoomDirection, e);
    });

    // 处理view-load-url事件
    ipcMain.on(`view-load-url-${id}`, (e, tabId: string, url: string) => {
      console.log('[ViewManager] Received view-load-url event:', tabId, url);
      const view = this.views.get(parseInt(tabId));
      if (view) {
        console.log('[ViewManager] Loading URL in view:', url);
        view.webContents.loadURL(url);
      } else {
        console.log('[ViewManager] View not found for tabId:', tabId);
      }
    });

    ipcMain.on('reset-zoom', (e) => {
      this.resetZoom();
    });

    this.setBoundsListener();
  }

  public resetZoom() {
    this.selected.webContents.zoomFactor = 1;
    this.selected.emitEvent(
      'zoom-updated',
      this.selected.webContents.zoomFactor,
    );
    this.emitZoomUpdate();
  }

  public changeZoom(zoomDirection: 'in' | 'out', e?: any) {
    const newZoomFactor =
      this.selected.webContents.zoomFactor +
      (zoomDirection === 'in'
        ? ZOOM_FACTOR_INCREMENT
        : -ZOOM_FACTOR_INCREMENT);

    if (
      newZoomFactor <= ZOOM_FACTOR_MAX &&
      newZoomFactor >= ZOOM_FACTOR_MIN
    ) {
      this.selected.webContents.zoomFactor = newZoomFactor;
      this.selected.emitEvent(
        'zoom-updated',
        this.selected.webContents.zoomFactor,
      );
    } else {
      e?.preventDefault();
    }
    this.emitZoomUpdate();
  }

  public get selected() {
    return this.views.get(this.selectedId);
  }

  public get settingsView() {
    return Object.values(this.views).find((r) =>
      r.url.startsWith(`${ELECTRON_WEBUI_BASE_URL}settings`),
    );
  }

  public findByKey(key: string): View {
    return Object.values(this.views).find((r) =>
      r.url.includes(`${key}`),
    );
  }

  public create(
    details: chrome.tabs.CreateProperties,
    isNext = false,
    sendMessage = true,
  ) {
    const view = new View(this.window, details.url, this.incognito);

    const {webContents} = view.browserView;
    const {id} = view;

    this.views.set(id, view);

    if (process.env.ENABLE_EXTENSIONS) {
      //extensions.tabs.observe(webContents);
    }

    webContents.once('destroyed', () => {
      this.views.delete(id);
    });


    if (sendMessage) {
      this.window.send('create-tab', {...details}, isNext, id);
    }
    return view;
  }

  public removeByTabId(id: number) {
    this.window.send('remove-tab', id);
  }

  public clear() {
    this.window.win.setBrowserView(null);
    Object.values(this.views).forEach((x) => x.destroy());
  }

  public async select(id: number, focus = true, callExtension = false) {
    const {selected} = this;
    const view = this.views.get(id);

    if (!view) {
      return;
    }

    // 防止重复选择同一个tab，避免Chrome扩展系统的循环调用
    if (this.selectedId === id) {
      return;
    }

    this.selectedId = id;

    if (selected) {
      this.window.win.removeBrowserView(selected.browserView);
    }

    this.window.win.addBrowserView(view.browserView);

    if (focus) {
      view.webContents.focus();
    } else {
      this.window.webContents.focus();
    }

    this.window.updateTitle();
    view.updateBookmark();

    await this.fixBounds();

    view.updateNavigationState();

    this.emit('activated', id);
    
    if (callExtension) {
      Application.instance.sessions.chromeExtensions.selectTab(view.webContents);
    }
  }


  private timer: any = null;
  public async fixBounds() {
    return this.fixBounds0(0);
  }
  public async fixBounds0(count: number) {
    const view = this.selected;

    if (!view) {
      return;
    }
    
    try {
      if (this.timer) {
        clearTimeout(this.timer);
      }
    } catch (e) {
      // Timer cleanup error, continue
    }

    const {width, height} = this.window.win.getContentBounds();

    let toolbarContentHeight;
    try {
      toolbarContentHeight = await this.window.win.webContents
        .executeJavaScript(`
        document.getElementById('app').offsetHeight
      `);
    } catch (error) {
      return;
    }
    
    if (toolbarContentHeight <= 0 && count < 40) {
      this.timer = setTimeout(() => {
        this.fixBounds0(count + 1);
      }, 25);
      return;
    }

    // AI工具栏现在由React组件实现，需要为其预留空间
    const aiToolbarWidth = 80; // 固定80px宽度

    const newBounds = {
      x: this.fullscreen ? 0 : aiToolbarWidth, // 全屏时不预留空间，否则预留64px
      y: this.fullscreen ? 0 : toolbarContentHeight,
      width: this.fullscreen ? width : width - aiToolbarWidth, // 全屏时占满宽度
      height: this.fullscreen ? height : height - toolbarContentHeight,
    };

    // 确保高度不为负数
    if (newBounds.height < 0) {
      newBounds.height = 100; // 最小高度
    }

    // 获取实际的BrowserView边界进行比较，而不是使用缓存的view.bounds
    const actualBounds = view.browserView.getBounds();

    // 使用实际边界进行比较，而不是缓存值
    if (JSON.stringify(newBounds) !== JSON.stringify(actualBounds)) {
      try {
        view.browserView.setBounds(newBounds);
        view.bounds = newBounds;
      } catch (error) {
        // Bounds setting error, continue
      }
    }
  }

  private setBoundsListener() {
    // resize the BrowserView's height when the toolbar height changes
    // ex: when the bookmarks bar appears
    this.window.webContents.executeJavaScript(`
        const {ipcRenderer} = require('electron');
        const resizeObserver = new ResizeObserver(([{ contentRect }]) => {
          ipcRenderer.send('resize-height');
        });
        const app = document.getElementById('app');
        if (app && app instanceof Element) {
          resizeObserver.observe(app);
        }
      `);

    this.window.webContents.on('ipc-message', (e, message) => {
      if (message === 'resize-height') {
        this.fixBounds();
      }
    });
  }

  public destroy(id: number) {
    const view = this.views.get(id);

    this.views.delete(id);

    if (view && !view.browserView.webContents.isDestroyed()) {
      this.window.win.removeBrowserView(view.browserView);
      view.destroy();
      this.emit('removed', id);
    }
  }

  public emitZoomUpdate(showDialog = true) {
    Application.instance.dialogs
      .getDynamic('zoom')
      ?.browserView?.webContents?.send(
      'zoom-factor-updated',
      this.selected.webContents.zoomFactor,
    );

    this.window.webContents.send(
      'zoom-factor-updated',
      this.selected.webContents.zoomFactor,
      showDialog,
    );
  }
}
