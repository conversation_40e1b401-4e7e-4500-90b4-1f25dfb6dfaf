import { observer } from 'mobx-react-lite';
import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
// 移除ToolbarButton导入，使用原生button元素
import store from '../../store';
import { eventUtils } from '@browser/core/utils/platform-lite';

interface AIToolbarProps {
  className?: string;
}

export const AIToolbar = observer(({ className }: AIToolbarProps) => {
  const [showLabels, setShowLabels] = React.useState(false);

  // 处理工具点击 - 直接调用store方法，无需IPC
  const handleToolClick = React.useCallback((toolId: string) => {
    console.log('[AIToolbar] Tool clicked:', toolId);

    // 检查是否已经有对应的标签页
    const existingTab = store.tabs.list.find(tab => 
      tab.url.includes(`ai-${toolId}`)
    );

    if (existingTab) {
      // 切换到已存在的标签页
      store.tabs.selectTab(existingTab.id);
    } else {
      // 创建新的AI工具标签页
      eventUtils.send(`add-tab-${store.windowId}`, {
        url: `mario-ai://ai-${toolId}`,
        active: true,
      });
    }
  }, []);

  // 切换标签显示
  const toggleLabels = React.useCallback(() => {
    setShowLabels(prev => !prev);
  }, []);

  // 移除主题切换功能

  // 键盘快捷键处理
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.altKey) {
        switch (e.key) {
          case '1':
            e.preventDefault();
            handleToolClick('notes');
            break;
          case '2':
            e.preventDefault();
            handleToolClick('memory');
            break;
          case '3':
            e.preventDefault();
            handleToolClick('clipboard');
            break;
          case '0':
            e.preventDefault();
            handleToolClick('settings');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleToolClick]);

  // 工具栏样式 - 关键：使用极高的z-index确保在BrowserView之上
  const toolbarClasses = cn(
    'fixed left-0 top-0 h-full flex flex-col',
    'border-r border-mario-border',
    'transition-all duration-200 ease-in-out',
    'z-[999999]', // 极高的z-index确保在BrowserView之上
    showLabels ? 'w-30' : 'w-16',
    className
  );

  // 工具栏背景样式 - 支持渐变
  const toolbarStyle: React.CSSProperties = {
    background: 'var(--mario-ai-toolbar-bg)',
  };

  // Logo区域样式 - 缩小尺寸
  const logoClasses = cn(
    'flex items-center justify-center h-10 mt-3 mb-3',
    'text-lg opacity-70 cursor-pointer',
    'hover:opacity-90 transition-all duration-200 hover:scale-110'
  );

  // 工具按钮区域样式
  const toolsClasses = cn(
    'flex-1 flex flex-col gap-2 px-2'
  );

  // 设置区域样式
  const settingsClasses = cn(
    'p-2 mb-2'
  );

  // 标签样式
  const labelClasses = cn(
    'text-xs text-mario-text-secondary mt-1 text-center',
    'transition-opacity duration-200',
    showLabels ? 'opacity-100' : 'opacity-0'
  );

  return (
    <div className={toolbarClasses} style={toolbarStyle}>
      {/* Logo区域 - 点击切换标签显示 */}
      <div className={logoClasses} onClick={toggleLabels} title="切换标签显示">
        🤖
        {showLabels && (
          <div className="absolute left-16 bg-mario-tooltip text-mario-tooltip-text px-2 py-1 rounded text-xs whitespace-nowrap">
            Mario AI
          </div>
        )}
      </div>

      {/* 移除主题切换按钮 */}

      {/* 工具按钮区域 */}
      <div className={toolsClasses}>
        {/* 智能笔记 */}
        <div className="flex flex-col items-center">
          <button
            onClick={() => handleToolClick('notes')}
            title="智能笔记 - 创建和管理AI增强的笔记 (Alt+1)"
            className="w-10 h-10 rounded-lg flex items-center justify-center text-lg hover:bg-mario-hover active:bg-mario-active transition-all duration-200 hover:scale-110"
          >
            📝
          </button>
          <div className={labelClasses}>笔记</div>
        </div>

        {/* AI记忆 */}
        <div className="flex flex-col items-center">
          <button
            onClick={() => handleToolClick('memory')}
            title="AI记忆 - 存储和检索重要信息 (Alt+2)"
            className="w-10 h-10 rounded-lg flex items-center justify-center text-lg hover:bg-mario-hover active:bg-mario-active transition-all duration-200 hover:scale-110"
          >
            🧠
          </button>
          <div className={labelClasses}>记忆</div>
        </div>

        {/* 智能剪贴板 */}
        <div className="flex flex-col items-center">
          <button
            onClick={() => handleToolClick('clipboard')}
            title="智能剪贴板 - 管理剪贴板历史和内容 (Alt+3)"
            className="w-10 h-10 rounded-lg flex items-center justify-center text-lg hover:bg-mario-hover active:bg-mario-active transition-all duration-200 hover:scale-110"
          >
            📋
          </button>
          <div className={labelClasses}>剪贴板</div>
        </div>
      </div>

      {/* 设置按钮区域 */}
      <div className={settingsClasses}>
        <div className="flex flex-col items-center">
          <button
            onClick={() => handleToolClick('settings')}
            title="设置 (Alt+0)"
            className="w-10 h-10 rounded-lg flex items-center justify-center text-lg hover:bg-mario-hover active:bg-mario-active transition-all duration-200 hover:scale-110"
          >
            ⚙️
          </button>
          <div className={labelClasses}>设置</div>
        </div>
      </div>
    </div>
  );
});

export default AIToolbar;
