<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题修复验证</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            transition: background-color 0.15s ease-out, color 0.15s ease-out;
        }

        /* 浅色主题 */
        [data-theme="light"] {
            --titlebar-bg: #f3f3fa;
            --toolbar-bg: #f3f3fa;
            --titlebar-text: #000000;
            --toolbar-text: #000000;
        }

        [data-theme="light"] body {
            background: var(--titlebar-bg);
            color: var(--titlebar-text);
        }

        /* 深色主题 */
        [data-theme="dark"] {
            --titlebar-bg: #1c1c1c;
            --toolbar-bg: #1c1c1c;
            --titlebar-text: #ffffff;
            --toolbar-text: #ffffff;
        }

        [data-theme="dark"] body {
            background: var(--titlebar-bg);
            color: var(--titlebar-text);
        }

        /* 前卫主题 */
        [data-theme="futuristic"] {
            --titlebar-bg: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            --toolbar-bg: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
            --titlebar-text: rgba(255, 255, 255, 0.95);
            --toolbar-text: rgba(255, 255, 255, 0.95);
        }

        [data-theme="futuristic"] body {
            background: var(--titlebar-bg);
            color: var(--titlebar-text);
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .titlebar-demo {
            background: var(--titlebar-bg);
            color: var(--titlebar-text);
            padding: 10px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            transition: background-color 0.15s ease-out, color 0.15s ease-out;
        }

        .toolbar-demo {
            background: var(--toolbar-bg);
            color: var(--toolbar-text);
            padding: 10px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            transition: background-color 0.15s ease-out, color 0.15s ease-out;
        }

        .theme-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .theme-button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .theme-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .theme-button.light {
            background: #f3f3fa;
            color: #000000;
        }

        .theme-button.dark {
            background: #1c1c1c;
            color: #ffffff;
        }

        .theme-button.futuristic {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: rgba(255, 255, 255, 0.95);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .test-results {
            margin-top: 30px;
        }

        .test-item {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            background: rgba(0, 123, 255, 0.1);
        }

        .current-theme {
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 主题修复验证页面</h1>
        <p>测试深色主题和前卫主题的修复效果</p>

        <div class="current-theme">
            当前主题: <span id="current-theme-display">未知</span>
        </div>

        <div class="theme-buttons">
            <button class="theme-button light" onclick="setTheme('light')">
                🌞 浅色主题
            </button>
            <button class="theme-button dark" onclick="setTheme('dark')">
                🌙 深色主题
            </button>
            <button class="theme-button futuristic" onclick="setTheme('futuristic')">
                🚀 前卫主题
            </button>
        </div>

        <div id="status" class="status success">
            ✅ 主题系统已就绪，请点击上方按钮测试主题切换
        </div>

        <div class="titlebar-demo">
            <strong>标题栏演示</strong><br>
            这里模拟浏览器标题栏的外观，应该使用 --titlebar-bg 背景色
        </div>

        <div class="toolbar-demo">
            <strong>工具栏演示</strong><br>
            这里模拟浏览器工具栏的外观，应该使用 --toolbar-bg 背景色
        </div>

        <div class="test-results">
            <h3>🧪 测试结果</h3>
            <div class="test-item">
                <strong>浅色主题:</strong> 标题栏和工具栏应该是浅灰色 (#f3f3fa)，文本为黑色
            </div>
            <div class="test-item">
                <strong>深色主题:</strong> 标题栏和工具栏应该是深灰色 (#1c1c1c)，文本为白色
            </div>
            <div class="test-item">
                <strong>前卫主题:</strong> 标题栏和工具栏应该是深蓝色渐变，文本为白色
            </div>
            <div class="test-item">
                <strong>切换测试:</strong> 主题切换应该平滑，无闪烁，无延迟
            </div>
        </div>

        <div style="margin-top: 30px;">
            <h3>🔍 调试信息</h3>
            <div id="debug-info" style="font-family: monospace; background: rgba(0,0,0,0.1); padding: 15px; border-radius: 4px;">
                <div>data-theme: <span id="data-theme">-</span></div>
                <div>--titlebar-bg: <span id="titlebar-bg">-</span></div>
                <div>--toolbar-bg: <span id="toolbar-bg">-</span></div>
                <div>--titlebar-text: <span id="titlebar-text">-</span></div>
            </div>
        </div>
    </div>

    <script>
        // 主题切换函数
        function setTheme(themeName) {
            console.log('Setting theme:', themeName);
            
            const root = document.documentElement;
            
            // 更新状态显示
            updateStatus(`正在切换到${getThemeDisplayName(themeName)}...`);
            
            // 移除旧主题
            root.classList.remove('dark');
            root.removeAttribute('data-theme');
            
            // 设置新主题
            if (themeName === 'dark') {
                root.setAttribute('data-theme', 'dark');
                root.classList.add('dark');
            } else if (themeName === 'futuristic') {
                root.setAttribute('data-theme', 'futuristic');
            } else {
                root.setAttribute('data-theme', 'light');
            }
            
            // 更新显示
            setTimeout(() => {
                updateThemeDisplay();
                updateStatus(`✅ 已切换到${getThemeDisplayName(themeName)}`);
            }, 100);
        }

        function getThemeDisplayName(theme) {
            switch(theme) {
                case 'light': return '浅色主题';
                case 'dark': return '深色主题';
                case 'futuristic': return '前卫主题';
                default: return '未知主题';
            }
        }

        function updateStatus(message) {
            const status = document.getElementById('status');
            status.textContent = message;
        }

        function updateThemeDisplay() {
            const root = document.documentElement;
            const dataTheme = root.getAttribute('data-theme') || 'light';
            const computedStyle = getComputedStyle(root);
            
            // 更新当前主题显示
            document.getElementById('current-theme-display').textContent = getThemeDisplayName(dataTheme);
            
            // 更新调试信息
            document.getElementById('data-theme').textContent = dataTheme;
            document.getElementById('titlebar-bg').textContent = computedStyle.getPropertyValue('--titlebar-bg') || '未定义';
            document.getElementById('toolbar-bg').textContent = computedStyle.getPropertyValue('--toolbar-bg') || '未定义';
            document.getElementById('titlebar-text').textContent = computedStyle.getPropertyValue('--titlebar-text') || '未定义';
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认主题
            setTheme('light');
            
            // 定期更新显示
            setInterval(updateThemeDisplay, 1000);
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.altKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        setTheme('light');
                        break;
                    case '2':
                        e.preventDefault();
                        setTheme('dark');
                        break;
                    case '3':
                        e.preventDefault();
                        setTheme('futuristic');
                        break;
                }
            }
        });
    </script>
</body>
</html>
