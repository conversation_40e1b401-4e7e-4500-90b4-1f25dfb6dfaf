# AI工具栏React组件集成方案（方案G）

## 概述

将AI工具栏作为React组件直接集成到主窗口，完全替代当前的轻量级子窗口方案。

## 方案对比

| 特性 | 当前方案（轻量子窗口） | 方案G（React组件） |
|------|----------------------|-------------------|
| 内存占用 | 5-15MB | 0MB (集成到主进程) |
| 启动时间 | 50-100ms | <10ms |
| 视觉一致性 | 中等（独立窗口） | 完美（原生集成） |
| 主题同步 | 需要IPC | 自动同步 |
| 开发复杂度 | 中（子窗口+IPC） | 低（React组件） |
| 跨平台兼容性 | 中等（需特殊处理） | 完美（无平台差异） |
| 调试难度 | 中等（多窗口） | 简单（单应用） |

## 核心实现

### 1. AIToolbar React组件

**文件**: `web/browser/src/views/app/components/AIToolbar/index.tsx`

```typescript
import { observer } from 'mobx-react-lite';
import * as React from 'react';
import { cn } from '@browser/utils/tailwind-helpers';
import { ToolbarButton } from '../ToolbarButton';
import store from '../../store';

interface AIToolbarProps {
  className?: string;
}

export const AIToolbar = observer(({ className }: AIToolbarProps) => {
  const handleToolClick = (toolId: string) => {
    // 直接调用store方法，无需IPC
    switch (toolId) {
      case 'notes':
        store.tabs.addTab({ url: 'mario-ai://ai-notes', active: true });
        break;
      case 'memory':
        store.tabs.addTab({ url: 'mario-ai://ai-memory', active: true });
        break;
      case 'clipboard':
        store.tabs.addTab({ url: 'mario-ai://ai-clipboard', active: true });
        break;
      case 'settings':
        store.tabs.addTab({ url: 'mario-ai://settings', active: true });
        break;
    }
  };

  // 工具栏样式 - 完全通栏效果
  const toolbarClasses = cn(
    'fixed left-0 top-0 z-50 h-full w-16',
    'flex flex-col bg-mario-ai-toolbar border-r border-mario-border',
    'transition-all duration-200 ease-in-out',
    className
  );

  const logoClasses = cn(
    'flex items-center justify-center h-12 mt-2 mb-4',
    'text-2xl opacity-60'
  );

  const toolsClasses = cn(
    'flex-1 flex flex-col gap-2 px-2'
  );

  const settingsClasses = cn(
    'p-2 mb-2'
  );

  return (
    <div className={toolbarClasses}>
      {/* Logo区域 */}
      <div className={logoClasses}>
        🤖
      </div>

      {/* 工具按钮区域 */}
      <div className={toolsClasses}>
        <ToolbarButton
          icon="📝"
          onClick={() => handleToolClick('notes')}
          tooltip="智能笔记 - 创建和管理AI增强的笔记"
          size={24}
          className="w-12 h-12"
        />
        <ToolbarButton
          icon="🧠"
          onClick={() => handleToolClick('memory')}
          tooltip="AI记忆 - 存储和检索重要信息"
          size={24}
          className="w-12 h-12"
        />
        <ToolbarButton
          icon="📋"
          onClick={() => handleToolClick('clipboard')}
          tooltip="智能剪贴板 - 管理剪贴板历史和内容"
          size={24}
          className="w-12 h-12"
        />
      </div>

      {/* 设置按钮区域 */}
      <div className={settingsClasses}>
        <ToolbarButton
          icon="⚙️"
          onClick={() => handleToolClick('settings')}
          tooltip="设置"
          size={24}
          className="w-12 h-12"
        />
      </div>
    </div>
  );
});
```

### 2. 集成到主App组件

**文件**: `web/browser/src/views/app/components/App/index.tsx`

```typescript
// 添加导入
import { AIToolbar } from '../AIToolbar';

const App = observer(() => {
  // 移除原有的marginLeft逻辑，改为条件渲染
  const appStyle: React.CSSProperties = {
    height: 'auto',
    // AI工具栏通过fixed定位，主内容需要左边距
    marginLeft: store.aiToolbarVisible ? '64px' : '0px',
    transition: 'margin-left 0.2s ease-in-out',
  };

  const lineStyle: React.CSSProperties = {
    height: 0,
    marginLeft: store.aiToolbarVisible ? '64px' : '0px',
    transition: 'margin-left 0.2s ease-in-out',
  };

  return (
    <>
      {/* AI工具栏 - 条件渲染 */}
      {store.aiToolbarVisible && <AIToolbar />}
      
      {/* 主应用内容 */}
      <div
        className={appClasses}
        style={appStyle}
        onMouseOver={store.isFullscreen ? onAppEnter : undefined}
        onMouseLeave={store.isFullscreen ? onAppLeave : undefined}
      >
        <UIStyle />
        <Titlebar />
        {store.settings.object.topBarVariant === 'default' && <Toolbar />}
        <BookmarkBar />
      </div>

      <div
        className={lineClasses}
        style={lineStyle}
        onMouseOver={onLineEnter}
      />
    </>
  );
});
```

### 3. 主题样式配置

**文件**: `web/browser/tailwind.config.js`

```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        'mario-ai-toolbar': 'var(--mario-ai-toolbar-bg)',
        'mario-border': 'var(--mario-border-color)',
      }
    }
  }
}
```

**文件**: `web/browser/src/core/styles/themes.css`

```css
:root {
  --mario-ai-toolbar-bg: #ffffff;
  --mario-border-color: #e0e0e0;
}

[data-theme="dark"] {
  --mario-ai-toolbar-bg: #2d2d2d;
  --mario-border-color: #555555;
}

[data-theme="mario-dark"] {
  --mario-ai-toolbar-bg: #1a1a1a;
  --mario-border-color: #404040;
}
```

### 4. 移除旧实现

1. 删除 `electron/src/main/services/lightweight-ai-toolbar.ts`
2. 从 `AppWindow` 中移除相关代码
3. 清理相关IPC处理器

## 实施步骤

1. **创建React组件** (1小时)
   - 创建AIToolbar组件
   - 添加主题样式配置

2. **集成到主应用** (30分钟)
   - 修改App组件
   - 更新样式和布局

3. **清理旧代码** (30分钟)
   - 删除LightweightAIToolbar类
   - 清理AppWindow集成代码

4. **测试验证** (1小时)
   - 功能测试
   - 主题切换测试
   - 跨平台测试

## 预期效果

- ✅ 内存占用：0MB额外开销
- ✅ 启动时间：<10ms
- ✅ 视觉效果：完美融合
- ✅ 主题同步：自动同步
- ✅ 开发体验：显著提升
