import { ITheme } from '@mario-ai/shared';

export class TailwindThemeManager {
  private static currentTheme: 'wexond-light' | 'wexond-dark' | 'futuristic' = 'futuristic'; // 默认使用前卫主题
  private static isInitialized = false;
  private static themeChangeTimeout: NodeJS.Timeout | null = null;

  /**
   * 初始化主题系统
   */
  static initialize() {
    if (this.isInitialized) return;

    // 添加CSS过渡动画 - 优化版本，避免闪烁
    const style = document.createElement('style');
    style.id = 'theme-transitions';
    style.textContent = `
      :root {
        transition: opacity 0.1s ease-out;
      }

      /* 只对特定元素添加过渡，避免全局闪烁 */
      .theme-transition {
        transition:
          background-color 0.15s ease-out,
          color 0.15s ease-out,
          border-color 0.15s ease-out;
      }

      /* 渐变背景的特殊处理 */
      [style*="gradient"] {
        transition: opacity 0.1s ease-out;
      }
    `;
    document.head.appendChild(style);

    this.isInitialized = true;
    console.log('[TailwindThemeManager] Initialized with transitions');
  }

  /**
   * 设置主题 - 优化版本，添加防抖
   * @param themeName 主题名称
   */
  static setTheme(themeName: 'wexond-light' | 'wexond-dark' | 'futuristic') {
    // 防抖：如果短时间内多次调用，只执行最后一次
    if (this.themeChangeTimeout) {
      clearTimeout(this.themeChangeTimeout);
    }

    this.themeChangeTimeout = setTimeout(() => {
      this.applyThemeImmediate(themeName);
    }, 16); // 一帧的时间
  }

  /**
   * 立即应用主题 - 防闪烁优化版本
   */
  private static applyThemeImmediate(themeName: 'wexond-light' | 'wexond-dark' | 'futuristic') {
    if (!this.isInitialized) {
      this.initialize();
    }

    const root = document.documentElement;
    console.log('[TailwindThemeManager] Applying theme:', themeName);

    // 防闪烁：先设置opacity为0，避免主题切换时的视觉跳跃
    const originalOpacity = root.style.opacity;
    root.style.opacity = '0.99'; // 使用0.99而不是0，避免完全隐藏

    // 使用requestAnimationFrame确保DOM更新的流畅性
    requestAnimationFrame(() => {
      // 原子性操作：一次性完成所有主题相关的DOM更新
      root.classList.remove('dark');
      root.removeAttribute('data-theme');

      // 设置新主题
      if (themeName === 'wexond-dark') {
        root.setAttribute('data-theme', 'dark');
        root.classList.add('dark');
      } else if (themeName === 'futuristic') {
        root.setAttribute('data-theme', 'futuristic');
      } else {
        root.setAttribute('data-theme', 'light');
      }

      this.currentTheme = themeName;

      // 恢复opacity，确保平滑显示
      requestAnimationFrame(() => {
        root.style.opacity = originalOpacity || '';

        // 延迟触发事件，确保DOM更新完成
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('theme-changed', {
            detail: { theme: themeName }
          }));
        }, 0);
      });
    });
  }

  /**
   * 获取当前主题
   */
  static getCurrentTheme() {
    return this.currentTheme;
  }

  /**
   * 检查是否为深色主题
   */
  static isDarkTheme() {
    return this.currentTheme === 'wexond-dark';
  }

  /**
   * 检查是否为前卫主题
   */
  static isFuturisticTheme() {
    return this.currentTheme === 'futuristic';
  }

  /**
   * 与现有主题系统集成
   * @param existingTheme 现有主题对象
   */
  static syncWithExistingTheme(existingTheme: ITheme) {
    const isDark = existingTheme['dialog.lightForeground'];
    this.setTheme(isDark ? 'wexond-dark' : 'wexond-light');
  }

  /**
   * 从主题名称同步
   * @param themeName 主题名称字符串
   */
  static syncFromThemeName(themeName: string) {
    if (themeName === 'wexond-dark') {
      this.setTheme('wexond-dark');
    } else {
      this.setTheme('wexond-light');
    }
  }

  /**
   * 检测系统主题偏好
   */
  static detectSystemTheme(): 'wexond-light' | 'wexond-dark' {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'wexond-dark';
    }
    return 'wexond-light';
  }

  /**
   * 设置主题（支持自动检测）- 优化版本
   * @param themeName 主题名称或'auto'
   * @param isAuto 是否为自动模式
   */
  static setThemeWithAuto(themeName: string, isAuto: boolean = false) {
    console.log('[TailwindThemeManager] setThemeWithAuto called:', { themeName, isAuto });

    if (isAuto || themeName === 'auto') {
      const systemTheme = this.detectSystemTheme();
      console.log('[TailwindThemeManager] Using system theme:', systemTheme);
      this.setTheme(systemTheme);
    } else if (themeName === 'mario-futuristic') {
      // 支持前卫主题
      console.log('[TailwindThemeManager] Setting futuristic theme');
      this.setTheme('futuristic');
    } else if (themeName === 'wexond-dark') {
      // 支持深色主题
      console.log('[TailwindThemeManager] Setting dark theme');
      this.setTheme('wexond-dark');
    } else {
      // 默认浅色主题
      console.log('[TailwindThemeManager] Setting light theme');
      this.setTheme('wexond-light');
    }
  }

  /**
   * 通知AI工具栏主题变化
   * @param theme 主题名称
   */
  private static notifyAIToolbarThemeChange(theme: string) {
    try {
      const { ipcRenderer } = require('electron');
      const windowId = (window as any).windowId || 1;
      ipcRenderer.send(`ai-toolbar-theme-change-${windowId}`, theme);
      console.log('[TailwindThemeManager] Notified AI toolbar of theme change:', theme);
    } catch (error) {
      console.warn('[TailwindThemeManager] Failed to notify AI toolbar theme change:', error);
    }
  }

  /**
   * 初始化主题系统
   * @param initialTheme 初始主题
   */
  static initialize(initialTheme?: string | ITheme) {
    if (typeof initialTheme === 'string') {
      this.syncFromThemeName(initialTheme);
    } else if (initialTheme && typeof initialTheme === 'object') {
      this.syncWithExistingTheme(initialTheme);
    } else {
      // 默认使用浅色主题
      this.setTheme('wexond-light');
    }
  }
}
